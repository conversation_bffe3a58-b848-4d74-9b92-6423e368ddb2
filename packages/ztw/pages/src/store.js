import {
  createStore, combineReducers, compose, applyMiddleware,
} from 'redux';
import createDebounce from 'redux-debounced';
import promise from 'redux-promise';
import thunkMiddleware from 'redux-thunk';
import { reducer as formReducer } from 'redux-form';

import {
  accountIdsDropdown,
  activation,
  administration,
  adminManagement,
  adminNav,
  alerts,
  apiKeyManagement,
  appConnectorGroupDropdown,
  appConnectorNameDropdown,
  appliances,
  appliancesEntityDropdown,
  applicationSegmentDropdown,
  applicationServiceGroupDropdown,
  appSegmentDropdown,
  appSegmentGroupDropdown,
  auditLogs,
  auditLogsActions,
  auditLogsSubCategories,
  awsRegionsDropdown,
  awsRegionsExcludeDropdown,
  awsSupportedRegionsDropdown,
  azureRegionsDropdown,
  azureRegionsExcludeDropdown,
  azureResourceGroupsDropdown,
  azureStorageAccountsDropdown,
  azureSubscriptionsDropdown,
  azureSupportedRegionsDropdown,
  azureZone,
  branchCloudConnectorImages,
  branchConnectorGroupsDropdown,
  branchConnectors,
  branchLocationsDropdown,
  categoryDropdown,
  citiesDropdown,
  cloudConfigurationAdvancedSettings,
  cloudConnectorGroupsAzureDropdown,
  cloudConnectorGroupsDropdownAWS,
  cloudConnectors,
  cloudProviders,
  cloudProvidersAzure,
  connectorMonitoring,
  countryDropdown,
  dashboard,
  dateDuration,
  DestinationIPCountryDropdown,
  destinationIpGroups,
  deviceLogs,
  deviceTypeDropdown,
  directionDropdown,
  dnsApplicationDropdown,
  dnsForwardingRuleDropdown,
  dnsGatewayDropdown,
  dnsGatewayFlagsDropdown,
  dnsGatewayIdsDropdown,
  dnsGateways,
  dnsInsights,
  dnsLogs,
  dnsPolicies,
  dnsPolicyActionDropdown,
  dnsReqTypeDropdown,
  dnsResTypeDropdown,
  dnsRuleDropdown,
  domainCategoryDropdown,
  domainsDropdown,
  drillDown,
  duration,
  ecGroupDropdown,
  ecInstanceDropdown,
  ecMetricsRecordTypesDropdown,
  ecVmDropdown,
  edgeconnectors,
  edgeconnectorWizard,
  eusa,
  filters,
  firewallLogs,
  forwardingMethodDropdown,
  forwardingRuleDropdown,
  fwdTrafficDirectionDropdown,
  gatewayDropdown,
  gatewayLogAndControlDropdown,
  gateways,
  gatewayZiaDropdown,
  gcpRegionsDropdown,
  gcpRegionsExcludeDropdown,
  gcpZoneDropdown,
  gcpZoneExcludeDropdown,
  geoView,
  groupDropdown,
  help,
  helpNav,
  idpNameDropdown,
  ipDestinationGroupDropdown,
  destinationWorloadGroupsDropdown,
  ipDnsDestinationGroupDropdown,
  ipDestinationWorkloadGroupsDropdown,
  ipPool,
  ipPoolDropdown,
  ipSourceGroupDropdown,
  sourceWorkloadGroupsDropdown,
  locations,
  locationsDropdown,
  locationsExcludingDeletingEntries,
  locationsEntityDropdown,
  locationsExcludeDropdown,
  locationsGroupDropdown,
  locationsGroupTypeDropdown,
  locationsInsightsDropdown,
  locationsSublocationsDropdown,
  locationTemplates,
  locationTemplatesEntityDropdown,
  logAndControlFwdPolicies,
  logAndControlGateways,
  login,
  logs,
  logsNav,
  multiSelectDropdown,
  networkProtocolDropdown,
  networkServiceDropdown,
  networkServiceExcludeDropdown,
  networkServices,
  networkServicesDropdown,
  networkServicesGroupDropdown,
  notification,
  nssSettingsCloudFeeds,
  nssSettingsFeeds,
  nssSettingsServers,
  oneIdentityLogin,
  partnerIntegrationsAccountDropdown,
  partnerIntegrationsAccountGroupDropdown,
  partnerIntegrationsAws,
  partnerIntegrationsAwsGroups,
  partnerIntegrationsAzure,
  passwordExpiry,
  platformDropdown,
  policyNav,
  profile,
  projectIdsDropdown,
  provisioningTemplates,
  provisioningTemplatesBranch,
  provisioningTemplateWizard,
  provisioningTemplateWizardBranch,
  regionsDropdown,
  regionsExcludeDropdown,
  reqRuleDropdown,
  resRuleDropdown,
  roleManagement,
  rolesDropdown,
  searchNav,
  serverIpCategoryDropdown,
  sessionInsights,
  sessionLogs,
  sourceIPGroups,
  sso,
  sublocations,
  subscriptionIdsDropdown,
  systemUserDropdown,
  trafficFwdPolicies,
  trafficMonitoring,
  trafficTypeDropdown,
  tunnelInsights,
  tunnelLogs,
  users,
  vdiAgentApp,
  vdiDeviceManagement,
  vdiDeviceOsTypeEntityDropdown,
  vdiForwardingProfileDropdown,
  vdiProfile,
  vdiTemplates,
  vpnCredentialsDropdown,
  zoneDropdown,
  zeroTrustGateway,
  ztGatewayDashboard,
} from 'ducks';
import actionTypes from './ducks/login/action-types';

const unorderedReducers = {
  [accountIdsDropdown.REDUCER_KEY]: accountIdsDropdown.reducer,
  [activation.REDUCER_KEY]: activation.reducer,
  [administration.REDUCER_KEY]: administration.reducer,
  [adminManagement.REDUCER_KEY]: adminManagement.reducer,
  [adminNav.REDUCER_KEY]: adminNav.reducer,
  [alerts.REDUCER_KEY]: alerts.reducer,
  [apiKeyManagement.REDUCER_KEY]: apiKeyManagement.reducer,
  [appConnectorGroupDropdown.REDUCER_KEY]: appConnectorGroupDropdown.reducer,
  [appConnectorNameDropdown.REDUCER_KEY]: appConnectorNameDropdown.reducer,
  [appliances.REDUCER_KEY]: appliances.reducer,
  [appliancesEntityDropdown.REDUCER_KEY]: appliancesEntityDropdown.reducer,
  [applicationSegmentDropdown.REDUCER_KEY]: applicationSegmentDropdown.reducer,
  [applicationServiceGroupDropdown.REDUCER_KEY]: applicationServiceGroupDropdown.reducer,
  [appSegmentDropdown.REDUCER_KEY]: appSegmentDropdown.reducer,
  [appSegmentGroupDropdown.REDUCER_KEY]: appSegmentGroupDropdown.reducer,
  [auditLogs.REDUCER_KEY]: auditLogs.reducer,
  [auditLogsActions.REDUCER_KEY]: auditLogsActions.reducer,
  [auditLogsSubCategories.REDUCER_KEY]: auditLogsSubCategories.reducer,
  [awsRegionsDropdown.REDUCER_KEY]: awsRegionsDropdown.reducer,
  [awsRegionsExcludeDropdown.REDUCER_KEY]: awsRegionsExcludeDropdown.reducer,
  [awsSupportedRegionsDropdown.REDUCER_KEY]: awsSupportedRegionsDropdown.reducer,
  [azureRegionsDropdown.REDUCER_KEY]: azureRegionsDropdown.reducer,
  [azureRegionsExcludeDropdown.REDUCER_KEY]: azureRegionsExcludeDropdown.reducer,
  [azureResourceGroupsDropdown.REDUCER_KEY]: azureResourceGroupsDropdown.reducer,
  [azureStorageAccountsDropdown.REDUCER_KEY]: azureStorageAccountsDropdown.reducer,
  [azureSubscriptionsDropdown.REDUCER_KEY]: azureSubscriptionsDropdown.reducer,
  [azureSupportedRegionsDropdown.REDUCER_KEY]: azureSupportedRegionsDropdown.reducer,
  [azureZone.REDUCER_KEY]: azureZone.reducer,
  [branchCloudConnectorImages.REDUCER_KEY]: branchCloudConnectorImages.reducer,
  [branchConnectorGroupsDropdown.REDUCER_KEY]: branchConnectorGroupsDropdown.reducer,
  [branchConnectors.REDUCER_KEY]: branchConnectors.reducer,
  [branchLocationsDropdown.REDUCER_KEY]: branchLocationsDropdown.reducer,
  [categoryDropdown.REDUCER_KEY]: categoryDropdown.reducer,
  [citiesDropdown.REDUCER_KEY]: citiesDropdown.reducer,
  [cloudConfigurationAdvancedSettings.REDUCER_KEY]: cloudConfigurationAdvancedSettings.reducer,
  [cloudConnectorGroupsAzureDropdown.REDUCER_KEY]: cloudConnectorGroupsAzureDropdown.reducer,
  [cloudConnectorGroupsDropdownAWS.REDUCER_KEY]: cloudConnectorGroupsDropdownAWS.reducer,
  [cloudConnectors.REDUCER_KEY]: cloudConnectors.reducer,
  [cloudProviders.REDUCER_KEY]: cloudProviders.reducer,
  [cloudProvidersAzure.REDUCER_KEY]: cloudProvidersAzure.reducer,
  [connectorMonitoring.REDUCER_KEY]: connectorMonitoring.reducer,
  [countryDropdown.REDUCER_KEY]: countryDropdown.reducer,
  [dashboard.REDUCER_KEY]: dashboard.reducer,
  [dateDuration.REDUCER_KEY]: dateDuration.reducer,
  [DestinationIPCountryDropdown.REDUCER_KEY]: DestinationIPCountryDropdown.reducer,
  [destinationIpGroups.REDUCER_KEY]: destinationIpGroups.reducer,
  [deviceLogs.REDUCER_KEY]: deviceLogs.reducer,
  [deviceTypeDropdown.REDUCER_KEY]: deviceTypeDropdown.reducer,
  [directionDropdown.REDUCER_KEY]: directionDropdown.reducer,
  [dnsApplicationDropdown.REDUCER_KEY]: dnsApplicationDropdown.reducer,
  [dnsForwardingRuleDropdown.REDUCER_KEY]: dnsForwardingRuleDropdown.reducer,
  [dnsGatewayDropdown.REDUCER_KEY]: dnsGatewayDropdown.reducer,
  [dnsGatewayFlagsDropdown.REDUCER_KEY]: dnsGatewayFlagsDropdown.reducer,
  [dnsGatewayIdsDropdown.REDUCER_KEY]: dnsGatewayIdsDropdown.reducer,
  [dnsGateways.REDUCER_KEY]: dnsGateways.reducer,
  [dnsInsights.REDUCER_KEY]: dnsInsights.reducer,
  [dnsLogs.REDUCER_KEY]: dnsLogs.reducer,
  [dnsPolicies.REDUCER_KEY]: dnsPolicies.reducer,
  [dnsPolicyActionDropdown.REDUCER_KEY]: dnsPolicyActionDropdown.reducer,
  [dnsReqTypeDropdown.REDUCER_KEY]: dnsReqTypeDropdown.reducer,
  [dnsResTypeDropdown.REDUCER_KEY]: dnsResTypeDropdown.reducer,
  [dnsRuleDropdown.REDUCER_KEY]: dnsRuleDropdown.reducer,
  [domainCategoryDropdown.REDUCER_KEY]: domainCategoryDropdown.reducer,
  [domainsDropdown.REDUCER_KEY]: domainsDropdown.reducer,
  [drillDown.REDUCER_KEY]: drillDown.reducer,
  [duration.REDUCER_KEY]: duration.reducer,
  [ecGroupDropdown.REDUCER_KEY]: ecGroupDropdown.reducer,
  [ecInstanceDropdown.REDUCER_KEY]: ecInstanceDropdown.reducer,
  [ecMetricsRecordTypesDropdown.REDUCER_KEY]: ecMetricsRecordTypesDropdown.reducer,
  [ecVmDropdown.REDUCER_KEY]: ecVmDropdown.reducer,
  [edgeconnectors.REDUCER_KEY]: edgeconnectors.reducer,
  [edgeconnectorWizard.REDUCER_KEY]: edgeconnectorWizard.reducer,
  [eusa.REDUCER_KEY]: eusa.reducer,
  [filters.REDUCER_KEY]: filters.reducer,
  [firewallLogs.REDUCER_KEY]: firewallLogs.reducer,
  [forwardingMethodDropdown.REDUCER_KEY]: forwardingMethodDropdown.reducer,
  [forwardingRuleDropdown.REDUCER_KEY]: forwardingRuleDropdown.reducer,
  [fwdTrafficDirectionDropdown.REDUCER_KEY]: fwdTrafficDirectionDropdown.reducer,
  [gatewayDropdown.REDUCER_KEY]: gatewayDropdown.reducer,
  [gatewayLogAndControlDropdown.REDUCER_KEY]: gatewayLogAndControlDropdown.reducer,
  [gateways.REDUCER_KEY]: gateways.reducer,
  [gatewayZiaDropdown.REDUCER_KEY]: gatewayZiaDropdown.reducer,
  [gcpRegionsDropdown.REDUCER_KEY]: gcpRegionsDropdown.reducer,
  [gcpRegionsExcludeDropdown.REDUCER_KEY]: gcpRegionsExcludeDropdown.reducer,
  [gcpZoneDropdown.REDUCER_KEY]: gcpZoneDropdown.reducer,
  [gcpZoneExcludeDropdown.REDUCER_KEY]: gcpZoneExcludeDropdown.reducer,
  [geoView.REDUCER_KEY]: geoView.reducer,
  [groupDropdown.REDUCER_KEY]: groupDropdown.reducer,
  [help.REDUCER_KEY]: help.reducer,
  [helpNav.REDUCER_KEY]: helpNav.reducer,
  [idpNameDropdown.REDUCER_KEY]: idpNameDropdown.reducer,
  [ipDestinationGroupDropdown.REDUCER_KEY]: ipDestinationGroupDropdown.reducer,
  [destinationWorloadGroupsDropdown.REDUCER_KEY]: destinationWorloadGroupsDropdown.reducer,
  [ipDnsDestinationGroupDropdown.REDUCER_KEY]: ipDnsDestinationGroupDropdown.reducer,
  [ipPool.REDUCER_KEY]: ipPool.reducer,
  [ipPoolDropdown.REDUCER_KEY]: ipPoolDropdown.reducer,
  [ipSourceGroupDropdown.REDUCER_KEY]: ipSourceGroupDropdown.reducer,
  [sourceWorkloadGroupsDropdown.REDUCER_KEY]: sourceWorkloadGroupsDropdown.reducer,
  [locations.REDUCER_KEY]: locations.reducer,
  [locationsDropdown.REDUCER_KEY]: locationsDropdown.reducer,
  [locationsExcludingDeletingEntries.REDUCER_KEY]: locationsExcludingDeletingEntries.reducer,
  [locationsEntityDropdown.REDUCER_KEY]: locationsEntityDropdown.reducer,
  [locationsExcludeDropdown.REDUCER_KEY]: locationsExcludeDropdown.reducer,
  [locationsGroupDropdown.REDUCER_KEY]: locationsGroupDropdown.reducer,
  [locationsGroupTypeDropdown.REDUCER_KEY]: locationsGroupTypeDropdown.reducer,
  [locationsInsightsDropdown.REDUCER_KEY]: locationsInsightsDropdown.reducer,
  [locationsSublocationsDropdown.REDUCER_KEY]: locationsSublocationsDropdown.reducer,
  [locationTemplates.REDUCER_KEY]: locationTemplates.reducer,
  [locationTemplatesEntityDropdown.REDUCER_KEY]: locationTemplatesEntityDropdown.reducer,
  [logAndControlFwdPolicies.REDUCER_KEY]: logAndControlFwdPolicies.reducer,
  [logAndControlGateways.REDUCER_KEY]: logAndControlGateways.reducer,
  [login.REDUCER_KEY]: login.reducer,
  [logs.REDUCER_KEY]: logs.reducer,
  [logsNav.REDUCER_KEY]: logsNav.reducer,
  [multiSelectDropdown.REDUCER_KEY]: multiSelectDropdown.reducer,
  [networkProtocolDropdown.REDUCER_KEY]: networkProtocolDropdown.reducer,
  [networkServiceDropdown.REDUCER_KEY]: networkServiceDropdown.reducer,
  [networkServiceExcludeDropdown.REDUCER_KEY]: networkServiceExcludeDropdown.reducer,
  [networkServices.REDUCER_KEY]: networkServices.reducer,
  [networkServicesDropdown.REDUCER_KEY]: networkServicesDropdown.reducer,
  [networkServicesGroupDropdown.REDUCER_KEY]: networkServicesGroupDropdown.reducer,
  [notification.REDUCER_KEY]: notification.reducer,
  [nssSettingsCloudFeeds.REDUCER_KEY]: nssSettingsCloudFeeds.reducer,
  [nssSettingsFeeds.REDUCER_KEY]: nssSettingsFeeds.reducer,
  [nssSettingsServers.REDUCER_KEY]: nssSettingsServers.reducer,
  [oneIdentityLogin.REDUCER_KEY]: oneIdentityLogin.reducer,
  // eslint-disable-next-line max-len
  [partnerIntegrationsAccountDropdown.REDUCER_KEY]: partnerIntegrationsAccountDropdown.reducer,
  // eslint-disable-next-line max-len
  [partnerIntegrationsAccountGroupDropdown.REDUCER_KEY]: partnerIntegrationsAccountGroupDropdown.reducer,
  [partnerIntegrationsAws.REDUCER_KEY]: partnerIntegrationsAws.reducer,
  [partnerIntegrationsAwsGroups.REDUCER_KEY]: partnerIntegrationsAwsGroups.reducer,
  [partnerIntegrationsAzure.REDUCER_KEY]: partnerIntegrationsAzure.reducer,
  [passwordExpiry.REDUCER_KEY]: passwordExpiry.reducer,
  [platformDropdown.REDUCER_KEY]: platformDropdown.reducer,
  [policyNav.REDUCER_KEY]: policyNav.reducer,
  [profile.REDUCER_KEY]: profile.reducer,
  [projectIdsDropdown.REDUCER_KEY]: projectIdsDropdown.reducer,
  [provisioningTemplates.REDUCER_KEY]: provisioningTemplates.reducer,
  [provisioningTemplatesBranch.REDUCER_KEY]: provisioningTemplatesBranch.reducer,
  [provisioningTemplateWizard.REDUCER_KEY]: provisioningTemplateWizard.reducer,
  [provisioningTemplateWizardBranch.REDUCER_KEY]: provisioningTemplateWizardBranch.reducer,
  [regionsDropdown.REDUCER_KEY]: regionsDropdown.reducer,
  [regionsExcludeDropdown.REDUCER_KEY]: regionsExcludeDropdown.reducer,
  [reqRuleDropdown.REDUCER_KEY]: reqRuleDropdown.reducer,
  [resRuleDropdown.REDUCER_KEY]: resRuleDropdown.reducer,
  [roleManagement.REDUCER_KEY]: roleManagement.reducer,
  [rolesDropdown.REDUCER_KEY]: rolesDropdown.reducer,
  [searchNav.REDUCER_KEY]: searchNav.reducer,
  [serverIpCategoryDropdown.REDUCER_KEY]: serverIpCategoryDropdown.reducer,
  [sessionInsights.REDUCER_KEY]: sessionInsights.reducer,
  [sessionLogs.REDUCER_KEY]: sessionLogs.reducer,
  [sourceIPGroups.REDUCER_KEY]: sourceIPGroups.reducer,
  [sso.REDUCER_KEY]: sso.reducer,
  [sublocations.REDUCER_KEY]: sublocations.reducer,
  [subscriptionIdsDropdown.REDUCER_KEY]: subscriptionIdsDropdown.reducer,
  [systemUserDropdown.REDUCER_KEY]: systemUserDropdown.reducer,
  [trafficFwdPolicies.REDUCER_KEY]: trafficFwdPolicies.reducer,
  [trafficMonitoring.REDUCER_KEY]: trafficMonitoring.reducer,
  [trafficTypeDropdown.REDUCER_KEY]: trafficTypeDropdown.reducer,
  [tunnelInsights.REDUCER_KEY]: tunnelInsights.reducer,
  [tunnelLogs.REDUCER_KEY]: tunnelLogs.reducer,
  [users.REDUCER_KEY]: users.reducer,
  [vdiAgentApp.REDUCER_KEY]: vdiAgentApp.reducer,
  [vdiDeviceManagement.REDUCER_KEY]: vdiDeviceManagement.reducer,
  [vdiDeviceOsTypeEntityDropdown.REDUCER_KEY]: vdiDeviceOsTypeEntityDropdown.reducer,
  [vdiForwardingProfileDropdown.REDUCER_KEY]: vdiForwardingProfileDropdown.reducer,
  [vdiProfile.REDUCER_KEY]: vdiProfile.reducer,
  [vdiTemplates.REDUCER_KEY]: vdiTemplates.reducer,
  [vpnCredentialsDropdown.REDUCER_KEY]: vpnCredentialsDropdown.reducer,
  [zoneDropdown.REDUCER_KEY]: zoneDropdown.reducer,
  [zeroTrustGateway.REDUCER_KEY]: zeroTrustGateway.reducer,
  [ztGatewayDashboard.REDUCER_KEY]: ztGatewayDashboard.reducer,
  // eslint-disable-next-line max-len
  form: formReducer,
};

// we'll sort them by key so finding them and debugging is easier
const orderedReducers = Object.keys(unorderedReducers)
  .sort()
  .reduce((hash, key) => ({
    ...hash,
    [key]: unorderedReducers[key],
  }), {});

export function configureStore(initialState = {}, customReducers = {}) {
  // Allow custom reducers to be passed in, defaulting to the full set
  const reducersToUse = Object.keys(customReducers).length > 0 ? customReducers : orderedReducers;
  const reducer = combineReducers(reducersToUse);

  const rootReducer = (state, action) => {
    // in case of Logout restore store
    // eslint-disable-next-line import/no-named-as-default-member
    if (action.type === actionTypes.RESET_STORE) {
      // eslint-disable-next-line
      state = undefined;
    }
    return reducer(state, action);
  };

  const middleware = [
    // fuxA(), // google analytics, mixpanel...future use
    createDebounce(),
    promise,
    thunkMiddleware, // lets us dispatch() functions
  ];

  // Safe check for window object and Redux DevTools
  const composeEnhancers = (typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) || compose;

  const store = createStore(
    rootReducer,
    initialState,
    composeEnhancers(applyMiddleware(...middleware)),
  );

  return store;
}

// Export a lightweight store configuration for library usage
export function configureLightweightStore(initialState = {}) {
  // Only include essential reducers for the AppLayout and its components
  const essentialReducers = {
    // Core authentication and user management
    [login.REDUCER_KEY]: login.reducer,

    // Notification system (used by Notification component)
    [notification.REDUCER_KEY]: notification.reducer,

    // EUSA agreement system (used by EUSAConfirmationModal and EUSABanner)
    [eusa.REDUCER_KEY]: eusa.reducer,

    // Gateways functionality (main feature)
    [gateways.REDUCER_KEY]: gateways.reducer,

    // Redux Form support
    form: formReducer,
  };

  return configureStore(initialState, essentialReducers);
}

export default configureStore;
