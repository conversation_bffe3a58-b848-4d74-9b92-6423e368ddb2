// Import library-specific styles
import './zuxp-layout/zuxp.scss';

// Export main pages (individual exports to avoid module resolution issues)
export { default as Gateways } from './pages/administration/gateways/Gateways';
export { default as AppProvider } from './zuxp-layout/AppProvider';
export { default as AppLayout } from './zuxp-layout/AppLayout';

// Export store configuration for external use
export { configureStore, configureLightweightStore } from './store';

// Export individual reducers for selective usage
export * as reducers from './ducks';

// Export utility functions
export { default as actionTypes } from './ducks/login/action-types';
export { refreshOneUILibrary } from './ducks/login';