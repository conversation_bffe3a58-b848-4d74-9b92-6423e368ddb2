import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// import Navbar from 'components/navbar';
import Notification from 'components/notification/Notification';
// import Help from 'components/help';
// import Footer from 'components/footer';
// import { Outlet, useLocation } from 'react-router-dom';
// import PasswordExpiryModal from 'components/PasswordExpiryModal';
import Splashscreen from './splashscreen';
// import LogoutSpinner from './LogoutSpinner';
import EUSAConfirmationModal from './eusa';
import EUSABanner from './eusa/EUSABanner';
import Spinner from 'components/spinner';
import { useDispatch } from 'react-redux';
import { refreshOneUI } from 'ducks/login';

function AppLayout({ children }) {
  // const location = useLocation();
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(true);
  useEffect(() => {
    dispatch(refreshOneUI()).then((response) => {
      if (response) {
        setLoading(false);
      }
    });
  }, [dispatch]);
  if (loading) return <Spinner />;

  return (
    <div className="page">
      <Notification />
      <EUSAConfirmationModal />
      <EUSABanner />
      {/* <Splashscreen /> */}
      <div id="noprint" className="noprint page">
        {/* <LogoutSpinner /> */}
        <>
          {children}
        </>
      </div>
      {/* <PasswordExpiryModal callTogglePasswordExpiryChangeListener={(str) => str} /> */}
    </div>
  );
}

AppLayout.propTypes = {
  classes: PropTypes.shape({}),
};

AppLayout.defaultProps = {
  classes: {},
};

export default AppLayout;
