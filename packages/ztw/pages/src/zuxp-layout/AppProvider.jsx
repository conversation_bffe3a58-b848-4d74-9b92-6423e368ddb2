'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureStore } from './../store';
// import AppLayout from './AppLayout';

export const store = configureStore();

function AppProvider({ children }) {
  return (
    <>
      <div className="ec-root-page">
        <Provider store={store}>
          {children}
        </Provider>
      </div>
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.any,
};
export default AppProvider;
