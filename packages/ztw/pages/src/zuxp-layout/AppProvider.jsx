'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureStore } from './../store.js';
import AppLayout from './AppLayout.jsx';

export const store = configureStore();

function AppProvider({ children }) {
  return (
    <>
      <div className="ec-root-page">
        <Provider store={store}>
          <AppLayout>{children}</AppLayout>
        </Provider>
      </div>
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.any,
};
export default AppProvider;
