'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureLightweightStore } from './../store';
import AppLayout from './AppLayout';

export const store = configureLightweightStore();

function AppProvider({ children }) {
  return (
    <>
      <div className="ec-root-page">
        <Provider store={store}>
          <AppLayout>{children}</AppLayout>
        </Provider>
      </div>
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.any,
};
export default AppProvider;
