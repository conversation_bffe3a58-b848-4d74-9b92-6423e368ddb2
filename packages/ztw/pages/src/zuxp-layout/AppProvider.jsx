'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureLightweightStore } from './../store';
import AppLayout from './AppLayout';

// Create store instance
export const store = configureLightweightStore();

function AppProvider({ children, useLayout = true }) {
  return (
    <>
      <div className="ec-root-page">
        <Provider store={store}>
          {useLayout ? (
            <AppLayout>{children}</AppLayout>
          ) : (
            children
          )}
        </Provider>
      </div>
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.any,
  useLayout: PropTypes.bool,
};
export default AppProvider;
